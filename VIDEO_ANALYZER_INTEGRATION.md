# Video Analyzer Integration Guide

This document describes the integration of the video-analyzer library into the tagTok application.

## Overview

The video-analyzer integration allows users to analyze video content using advanced AI models including:
- Vision models (like Llama3.2 Vision) for frame analysis
- Whisper for audio transcription
- Natural language processing for comprehensive video descriptions

## Features Implemented

### 1. Backend Integration
- **Database Schema**: Added `VideoAnalysis` model to store analysis results
- **Service Layer**: Created `VideoAnalysisService` for handling video analysis operations
- **API Endpoints**: Added REST endpoints for starting, monitoring, and retrieving analysis results
- **Async Processing**: Video analysis runs in background to avoid blocking the UI

### 2. Frontend Integration
- **New UI Components**: 
  - `VideoAnalysisResults` component for displaying analysis results
  - Analysis prompt modal for custom analysis requests
  - "Analyze with Video Analyzer" button in video interface
- **Real-time Updates**: Progress tracking and status updates during analysis
- **Error Handling**: Comprehensive error states and user feedback

### 3. API Endpoints

#### Start Video Analysis
```
POST /videos/{video_id}/analyze
```
Body:
```json
{
  "prompt": "Optional custom prompt for analysis",
  "max_frames": 30,
  "use_transcript": true,
  "analyzer_model": "llama3.2-vision"
}
```

#### Get Analysis Results
```
GET /videos/{video_id}/analysis
```

#### Get Analysis Status
```
GET /videos/{video_id}/analysis/status
```

#### Delete Analysis
```
DELETE /videos/{video_id}/analysis
```

## Installation & Setup

### 1. Install Dependencies

```bash
# Backend dependencies
cd backend
pip install -r requirements.txt

# This will install:
# - video-analyzer from GitHub
# - requests for HTTP calls
# - ffmpeg-python for video processing
```

### 2. Install System Dependencies

```bash
# Install FFmpeg (required by video-analyzer)
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt-get install ffmpeg

# Windows
# Download from https://ffmpeg.org/download.html
```

### 3. Install Ollama (for local analysis)

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull the vision model
ollama pull llama3.2-vision

# Start Ollama service
ollama serve
```

### 4. Database Migration

The new `VideoAnalysis` table will be created automatically when the application starts.

## Configuration

### Default Configuration
- **Model**: llama3.2-vision (via Ollama)
- **Max Frames**: 30 frames per video
- **Whisper Model**: medium
- **Temperature**: 0.2 (for consistent results)

### Using Cloud APIs (Optional)
You can configure the system to use cloud-based APIs instead of local Ollama:

```python
# In video_analysis_service.py, update default_config:
default_config = {
    "client": "openai_api",
    "api_key": "your-api-key",
    "api_url": "https://openrouter.ai/api/v1",
    "model": "meta-llama/llama-3.2-11b-vision-instruct:free"
}
```

## Usage

### 1. Analyze a Video
1. Navigate to a video detail page
2. Click "Analyze with Video Analyzer" button
3. Optionally enter a custom prompt (e.g., "What cooking techniques are shown?")
4. Click "Start Analysis"
5. Monitor progress in real-time
6. View results when complete

### 2. Analysis Results
The analysis provides:
- **Video Description**: Overall summary of video content
- **Frame-by-Frame Analysis**: Detailed analysis of key frames
- **Transcript Analysis**: Analysis based on audio content (if available)
- **Confidence Scores**: Quality metrics for the analysis
- **Metadata**: Analysis duration, model used, frame count

## Testing

Run the integration test:

```bash
cd backend
python3 test_video_analysis.py
```

Expected output:
```
Testing Video Analyzer Integration
==================================================
✓ Database Schema: PASSED
✓ Video Analysis Service: PASSED  
✓ API Endpoints: PASSED
✓ Mock Analysis: PASSED
⚠️ Video Analyzer Command: Requires installation

Test Results: 4/5 tests passed
```

## Troubleshooting

### Common Issues

1. **"video-analyzer command not found"**
   - Install the video-analyzer package: `pip install git+https://github.com/byjlw/video-analyzer.git`

2. **"FFmpeg not found"**
   - Install FFmpeg system dependency (see installation section)

3. **"Ollama connection failed"**
   - Ensure Ollama is running: `ollama serve`
   - Check if the model is available: `ollama list`

4. **Analysis fails with "Model not found"**
   - Pull the required model: `ollama pull llama3.2-vision`

5. **Out of memory errors**
   - Reduce max_frames parameter
   - Use a smaller Whisper model (e.g., "small" instead of "medium")

## Performance Considerations

- **Local vs Cloud**: Local analysis requires significant RAM/VRAM but provides privacy
- **Frame Sampling**: Adjust max_frames based on video length and available resources
- **Async Processing**: Analysis runs in background to maintain UI responsiveness
- **Caching**: Analysis results are stored in database to avoid re-processing

## Security Notes

- Video files are processed locally when using Ollama
- Cloud APIs may send video frames to external services
- Analysis results are stored in your local database
- No video content is transmitted unless using cloud APIs

## Future Enhancements

Potential improvements for future versions:
- Batch analysis for multiple videos
- Custom model fine-tuning
- Integration with additional vision models
- Export analysis results to various formats
- Analysis comparison between different models
