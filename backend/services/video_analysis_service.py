import os
import json
import asyncio
import subprocess
import tempfile
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from models.database import Video, VideoAnalysis
from models.schemas import VideoAnalysisCreate, VideoAnalysisUpdate, VideoAnalysisRequest
from utils.video_utils import get_video_metadata


class VideoAnalysisService:
    """Service for handling video analysis using the video-analyzer library"""
    
    def __init__(self, db: Session):
        self.db = db
        self.default_config = {
            "client": "ollama",
            "model": "llama3.2-vision",
            "ollama_url": "http://localhost:11434",
            "max_frames": 30,
            "whisper_model": "medium",
            "temperature": 0.2,
            "keep_frames": False,
            "output_dir": "/tmp/video_analysis"
        }
    
    def get_analysis_by_video_id(self, video_id: int) -> Optional[VideoAnalysis]:
        """Get video analysis by video ID"""
        return self.db.query(VideoAnalysis).filter(VideoAnalysis.video_id == video_id).first()
    
    def create_analysis(self, video_id: int, request: VideoAnalysisRequest) -> VideoAnalysis:
        """Create a new video analysis record"""
        analysis = VideoAnalysis(
            video_id=video_id,
            analysis_status="pending",
            analyzer_model=request.analyzer_model or self.default_config["model"],
            analyzer_config={
                "prompt": request.prompt,
                "max_frames": request.max_frames or self.default_config["max_frames"],
                "use_transcript": request.use_transcript,
                "temperature": self.default_config["temperature"]
            }
        )
        
        self.db.add(analysis)
        self.db.commit()
        self.db.refresh(analysis)
        return analysis
    
    def update_analysis_status(
        self, 
        analysis_id: int, 
        status: str, 
        progress: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update analysis status and progress"""
        analysis = self.db.query(VideoAnalysis).filter(VideoAnalysis.id == analysis_id).first()
        if not analysis:
            return False
        
        analysis.analysis_status = status
        if progress is not None:
            analysis.analysis_progress = max(0, min(100, progress))
        
        if status == "processing" and not analysis.started_at:
            analysis.started_at = datetime.utcnow()
        elif status in ["completed", "failed"]:
            analysis.completed_at = datetime.utcnow()
            if status == "completed":
                analysis.analysis_progress = 100
        
        if error_message:
            analysis.error_message = error_message
        
        self.db.commit()
        return True
    
    def update_analysis_results(
        self,
        analysis_id: int,
        results: Dict[str, Any]
    ) -> bool:
        """Update analysis with results from video-analyzer"""
        analysis = self.db.query(VideoAnalysis).filter(VideoAnalysis.id == analysis_id).first()
        if not analysis:
            return False
        
        # Extract results from video-analyzer output
        if "video_description" in results:
            analysis.video_description = results["video_description"]
        
        if "frame_analyses" in results:
            analysis.frame_analyses = results["frame_analyses"]
            analysis.key_frames_count = len(results["frame_analyses"])
        
        if "transcript_analysis" in results:
            analysis.transcript_analysis = results["transcript_analysis"]
        
        if "confidence_score" in results:
            analysis.confidence_score = results["confidence_score"]
        
        if "analysis_duration" in results:
            analysis.analysis_duration = results["analysis_duration"]
        
        self.db.commit()
        return True
    
    async def analyze_video(self, video_id: int, request: VideoAnalysisRequest) -> VideoAnalysis:
        """Start video analysis process"""
        # Get video record
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video with ID {video_id} not found")
        
        # Check if analysis already exists
        existing_analysis = self.get_analysis_by_video_id(video_id)
        if existing_analysis and existing_analysis.analysis_status == "processing":
            raise ValueError("Video analysis is already in progress")
        
        # Create or update analysis record
        if existing_analysis:
            analysis = existing_analysis
            analysis.analysis_status = "pending"
            analysis.analysis_progress = 0
            analysis.error_message = None
            analysis.started_at = None
            analysis.completed_at = None
            self.db.commit()
        else:
            analysis = self.create_analysis(video_id, request)
        
        # Start analysis in background
        asyncio.create_task(self._run_analysis(analysis.id, video.file_path, request))
        
        return analysis
    
    async def _run_analysis(self, analysis_id: int, video_path: str, request: VideoAnalysisRequest):
        """Run the actual video analysis using video-analyzer"""
        try:
            # Update status to processing
            self.update_analysis_status(analysis_id, "processing", 10)
            
            # Prepare video-analyzer command
            cmd = self._build_analyzer_command(video_path, request)
            
            # Update progress
            self.update_analysis_status(analysis_id, "processing", 30)
            
            # Run video-analyzer
            start_time = datetime.utcnow()
            result = await self._execute_analyzer(cmd)
            end_time = datetime.utcnow()
            
            # Update progress
            self.update_analysis_status(analysis_id, "processing", 80)
            
            # Parse results
            analysis_results = self._parse_analyzer_output(result, start_time, end_time)
            
            # Update analysis with results
            self.update_analysis_results(analysis_id, analysis_results)
            
            # Mark as completed
            self.update_analysis_status(analysis_id, "completed", 100)
            
        except Exception as e:
            error_msg = f"Video analysis failed: {str(e)}"
            self.update_analysis_status(analysis_id, "failed", error_message=error_msg)
    
    def _build_analyzer_command(self, video_path: str, request: VideoAnalysisRequest) -> List[str]:
        """Build the video-analyzer command"""
        cmd = [
            "video-analyzer",
            video_path,
            "--output", self.default_config["output_dir"],
            "--client", self.default_config["client"],
            "--model", request.analyzer_model or self.default_config["model"],
            "--max-frames", str(request.max_frames or self.default_config["max_frames"]),
            "--whisper-model", self.default_config["whisper_model"],
            "--temperature", str(self.default_config["temperature"]),
            "--log-level", "INFO"
        ]
        
        if request.prompt:
            cmd.extend(["--prompt", request.prompt])
        
        if not request.use_transcript:
            cmd.append("--no-transcript")
        
        if self.default_config["keep_frames"]:
            cmd.append("--keep-frames")
        
        return cmd
    
    async def _execute_analyzer(self, cmd: List[str]) -> Dict[str, Any]:
        """Execute the video-analyzer command"""
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise Exception(f"Video analyzer failed: {stderr.decode()}")
        
        # Try to find and parse the output JSON file
        output_dir = self.default_config["output_dir"]
        analysis_file = os.path.join(output_dir, "analysis.json")
        
        if os.path.exists(analysis_file):
            with open(analysis_file, 'r') as f:
                return json.load(f)
        else:
            # Fallback: parse stdout for basic results
            return {"video_description": stdout.decode().strip()}
    
    def _parse_analyzer_output(self, result: Dict[str, Any], start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Parse and structure the analyzer output"""
        duration = (end_time - start_time).total_seconds()
        
        parsed_result = {
            "analysis_duration": duration
        }
        
        # Extract video description
        if "video_description" in result:
            parsed_result["video_description"] = result["video_description"]
        elif "description" in result:
            parsed_result["video_description"] = result["description"]
        
        # Extract frame analyses
        if "frame_analyses" in result:
            parsed_result["frame_analyses"] = result["frame_analyses"]
        elif "frames" in result:
            parsed_result["frame_analyses"] = result["frames"]
        
        # Extract transcript analysis
        if "transcript" in result:
            parsed_result["transcript_analysis"] = result["transcript"]
        
        # Calculate confidence score (basic implementation)
        if "frame_analyses" in parsed_result:
            frame_count = len(parsed_result["frame_analyses"])
            if frame_count > 0:
                parsed_result["confidence_score"] = min(1.0, frame_count / 10.0)
        
        return parsed_result
    
    def delete_analysis(self, video_id: int) -> bool:
        """Delete video analysis for a video"""
        analysis = self.get_analysis_by_video_id(video_id)
        if analysis:
            self.db.delete(analysis)
            self.db.commit()
            return True
        return False
