from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional

from models.database import get_db
from models.schemas import VideoAnalysisResponse, VideoAnalysisRequest
from services.video_analysis_service import VideoAnalysisService
from services.video_service import VideoService

router = APIRouter(prefix="/videos", tags=["video-analysis"])


@router.post("/{video_id}/analyze", response_model=VideoAnalysisResponse)
async def start_video_analysis(
    video_id: int,
    request: VideoAnalysisRequest,
    db: Session = Depends(get_db)
):
    """Start video analysis for a specific video"""
    try:
        # Check if video exists
        video_service = VideoService(db)
        video = video_service.get_video_by_id(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Start analysis
        analysis_service = VideoAnalysisService(db)
        analysis = await analysis_service.analyze_video(video_id, request)
        
        return analysis
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start video analysis: {str(e)}"
        )


@router.get("/{video_id}/analysis", response_model=Optional[VideoAnalysisResponse])
async def get_video_analysis(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Get video analysis results for a specific video"""
    try:
        # Check if video exists
        video_service = VideoService(db)
        video = video_service.get_video_by_id(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Get analysis
        analysis_service = VideoAnalysisService(db)
        analysis = analysis_service.get_analysis_by_video_id(video_id)
        
        return analysis
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get video analysis: {str(e)}"
        )


@router.delete("/{video_id}/analysis")
async def delete_video_analysis(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Delete video analysis for a specific video"""
    try:
        # Check if video exists
        video_service = VideoService(db)
        video = video_service.get_video_by_id(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Delete analysis
        analysis_service = VideoAnalysisService(db)
        success = analysis_service.delete_analysis(video_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video analysis not found"
            )
        
        return {"message": "Video analysis deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete video analysis: {str(e)}"
        )


@router.get("/{video_id}/analysis/status")
async def get_analysis_status(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Get the current status of video analysis"""
    try:
        # Check if video exists
        video_service = VideoService(db)
        video = video_service.get_video_by_id(video_id)
        if not video:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Video not found"
            )
        
        # Get analysis status
        analysis_service = VideoAnalysisService(db)
        analysis = analysis_service.get_analysis_by_video_id(video_id)
        
        if not analysis:
            return {
                "status": "not_started",
                "progress": 0,
                "message": "No analysis has been started for this video"
            }
        
        return {
            "status": analysis.analysis_status,
            "progress": analysis.analysis_progress,
            "started_at": analysis.started_at,
            "completed_at": analysis.completed_at,
            "error_message": analysis.error_message
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis status: {str(e)}"
        )
