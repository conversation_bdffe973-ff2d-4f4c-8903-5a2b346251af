import React from 'react';
import { format } from 'date-fns';
import {
  EyeIcon,
  ClockIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

import { VideoAnalysis, FrameAnalysis } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface VideoAnalysisResultsProps {
  analysis: VideoAnalysis | null;
  isLoading?: boolean;
  error?: string;
}

const VideoAnalysisResults: React.FC<VideoAnalysisResultsProps> = ({
  analysis,
  isLoading = false,
  error
}) => {
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
          <span className="ml-3 text-gray-600">Loading analysis results...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center text-red-600 mb-4">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
          <h3 className="text-lg font-medium">Analysis Error</h3>
        </div>
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  if (!analysis) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8">
          <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Analysis Available</h3>
          <p className="text-gray-600">
            This video hasn't been analyzed yet. Click the "Analyze with Video Analyzer" button to start.
          </p>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'processing':
        return <ArrowPathIcon className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <EyeIcon className="h-6 w-6 text-gray-400 mr-3" />
            <h2 className="text-lg font-medium text-gray-900">Video Analysis Results</h2>
          </div>
          <div className="flex items-center">
            {getStatusIcon(analysis.analysis_status)}
            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(analysis.analysis_status)}`}>
              {analysis.analysis_status}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Progress Bar (for processing status) */}
        {analysis.analysis_status === 'processing' && (
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Analysis Progress</span>
              <span>{analysis.analysis_progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${analysis.analysis_progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Error Message */}
        {analysis.error_message && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Analysis Error</h3>
                <p className="mt-1 text-sm text-red-700">{analysis.error_message}</p>
              </div>
            </div>
          </div>
        )}

        {/* Analysis Metadata */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {analysis.analyzer_model && (
            <div>
              <dt className="text-sm font-medium text-gray-500">Model Used</dt>
              <dd className="mt-1 text-sm text-gray-900">{analysis.analyzer_model}</dd>
            </div>
          )}
          {analysis.key_frames_count && (
            <div>
              <dt className="text-sm font-medium text-gray-500">Frames Analyzed</dt>
              <dd className="mt-1 text-sm text-gray-900">{analysis.key_frames_count}</dd>
            </div>
          )}
          {analysis.confidence_score && (
            <div>
              <dt className="text-sm font-medium text-gray-500">Confidence Score</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {(analysis.confidence_score * 100).toFixed(1)}%
              </dd>
            </div>
          )}
          {analysis.analysis_duration && (
            <div>
              <dt className="text-sm font-medium text-gray-500">Analysis Duration</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatDuration(analysis.analysis_duration)}
              </dd>
            </div>
          )}
        </div>

        {/* Video Description */}
        {analysis.video_description && (
          <div>
            <div className="flex items-center mb-3">
              <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Video Description</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                {analysis.video_description}
              </p>
            </div>
          </div>
        )}

        {/* Transcript Analysis */}
        {analysis.transcript_analysis && (
          <div>
            <div className="flex items-center mb-3">
              <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Transcript Analysis</h3>
            </div>
            <div className="bg-blue-50 rounded-lg p-4">
              <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                {analysis.transcript_analysis}
              </p>
            </div>
          </div>
        )}

        {/* Frame Analyses */}
        {analysis.frame_analyses && analysis.frame_analyses.length > 0 && (
          <div>
            <div className="flex items-center mb-3">
              <ChartBarIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Frame-by-Frame Analysis</h3>
            </div>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {analysis.frame_analyses.map((frame: any, index: number) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm font-medium text-gray-900">
                      Frame {frame.frame_number || index + 1}
                    </span>
                    {frame.timestamp && (
                      <span className="text-xs text-gray-500">
                        {formatDuration(frame.timestamp)}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-700">{frame.description || frame}</p>
                  {frame.confidence && (
                    <div className="mt-2">
                      <span className="text-xs text-gray-500">
                        Confidence: {(frame.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Analysis Timestamps */}
        <div className="text-xs text-gray-500 border-t pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {analysis.started_at && (
              <div>
                Started: {format(new Date(analysis.started_at), 'PPp')}
              </div>
            )}
            {analysis.completed_at && (
              <div>
                Completed: {format(new Date(analysis.completed_at), 'PPp')}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoAnalysisResults;
